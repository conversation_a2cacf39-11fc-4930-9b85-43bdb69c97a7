<?php
require_once "includes/btcpay-gateway.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";
?>

<main>
  <section class="hero">
    <h1><?php echo $is_donation ? "Make a Donation" : "Pay for Services"; ?></h1>
  </section>

  <section class="section" id="payment-form">
    <h2><?php echo $is_donation ? "Support ELOH Processing with Bitcoin" : "Bitcoin Payment for Services"; ?></h2>
    
    <?php 
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';
        
        echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">';
        echo '<h3>❌ Error</h3>';
        
        switch ($error) {
            case 'invalid_data':
                echo '<p>Please check your input data:</p>';
                if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
                break;
            case 'btcpay_error':
                echo '<p>BTCPay Server error occurred.</p>';
                if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
                break;
            case 'configuration':
                echo '<p>Payment system configuration error.</p>';
                echo '<p>Please contact support.</p>';
                break;
            default:
                echo '<p>An unknown error occurred.</p>';
        }
        echo '</div>';
    }
    ?>
    
    <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🚀 Powered by BTCPay Server</h3>
        <p>Secure, self-hosted Bitcoin payment processing. Your payment goes directly to our Bitcoin wallet.</p>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li>✅ No intermediaries or third parties</li>
            <li>✅ Lightning Network support for instant payments</li>
            <li>✅ On-chain Bitcoin payments</li>
            <li>✅ Real-time payment confirmation</li>
        </ul>
    </div>
    
    <?php if ($is_donation): ?>
    <p>Your Bitcoin donation helps us expand our sustainable crypto mining operations and transition to 100% renewable energy.</p>
    <?php else: ?>
    <p>Pay for our consulting services, mining pool memberships, and other professional offerings using Bitcoin.</p>
    <?php endif; ?>

    <form method="POST" action="btcpay-process-payment.php" style="max-width: 600px; margin: 0 auto;">
      <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">
      
      <div style="margin-bottom: 20px;">
        <label for="amount" style="display: block; margin-bottom: 5px; font-weight: bold;">Amount (USD):</label>
        <input type="number" id="amount" name="amount" min="1" step="0.01" required 
               value="<?php echo htmlspecialchars($preset_amount); ?>"
               style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
        <small style="color: #666;">Minimum: $1.00 USD</small>
      </div>

      <div style="margin-bottom: 20px;">
        <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
        <input type="email" id="email" name="email" required 
               style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
               placeholder="<EMAIL>">
        <small style="color: #666;">For payment confirmation and receipt</small>
      </div>

      <?php if (!$is_donation): ?>
      <div style="margin-bottom: 20px;">
        <label for="service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
        <select id="service" name="service" required 
                style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          <option value="consulting">Crypto & Forex Consulting ($150/hour)</option>
          <option value="mining-pool">Mining Pool Membership ($200/year)</option>
          <option value="mining-services">Mining Services ($500/month)</option>
          <option value="analysis">Market Analysis Report ($99/report)</option>
          <option value="other">Other Services</option>
        </select>
      </div>
      <?php endif; ?>

      <div style="margin-bottom: 20px;">
        <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
        <textarea id="description" name="description" rows="3" 
                  style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                  placeholder="<?php echo $is_donation ? "Optional message with your donation" : "Describe the service you are paying for"; ?>"></textarea>
      </div>

      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h4>💰 Payment Methods Available:</h4>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li><strong>⚡ Lightning Network:</strong> Instant, low-fee payments</li>
          <li><strong>🔗 On-Chain Bitcoin:</strong> Traditional Bitcoin transactions</li>
        </ul>
        <p style="margin: 5px 0;"><small>You'll be redirected to BTCPay Server to complete your payment securely.</small></p>
      </div>

      <div style="text-align: center;">
        <button type="submit" class="cta-button" style="padding: 15px 30px; font-size: 1.1em;">
          <?php echo $is_donation ? "🚀 Proceed to Bitcoin Donation" : "🚀 Proceed to Bitcoin Payment"; ?>
        </button>
      </div>
    </form>
    
    <div style="text-align: center; margin-top: 30px;">
      <p><a href="index.php">← Back to Homepage</a></p>
    </div>
  </section>
</main>

<?php include "footer.php"; ?>
