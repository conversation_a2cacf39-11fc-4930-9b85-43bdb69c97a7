<?php
/**
 * NowPayments Configuration for ELOH Processing
 */

return [
    // API URLs
    'sandbox_url' => 'https://api-sandbox.nowpayments.io/v1',
    'live_url' => 'https://api.nowpayments.io/v1',

    // API Keys (Get from account-sandbox.nowpayments.io for testing)
    'sandbox_api_key' => 'your_sandbox_api_key_here', // Replace with sandbox API key
    'live_api_key' => 'your_live_api_key_here', // Replace with live API key

    // IPN Secret (for webhook validation)
    'ipn_secret' => 'your_ipn_secret_here', // Replace with your IPN secret

    // Wallet Settings (where you want to receive payments)
    'outcome_wallet' => [
        'btc' => 'your_btc_wallet_address',
        'eth' => 'your_eth_wallet_address',
        'usdt' => 'your_usdt_wallet_address'
    ],

    // Default Settings
    'default_currency' => 'btc',
    'environment' => 'live', // 'sandbox' or 'live'

    // Supported currencies for your business
    'supported_currencies' => [
        'btc' => [
            'name' => 'Bitcoin',
            'symbol' => 'BTC',
            'icon' => '₿',
            'min_amount' => 0.0001
        ],
        'eth' => [
            'name' => 'Ethereum',
            'symbol' => 'ETH',
            'icon' => 'Ξ',
            'min_amount' => 0.001
        ],
        'usdt' => [
            'name' => 'Tether',
            'symbol' => 'USDT',
            'icon' => '₮',
            'min_amount' => 1
        ],
        'usdc' => [
            'name' => 'USD Coin',
            'symbol' => 'USDC',
            'icon' => '$',
            'min_amount' => 1
        ],
        'ltc' => [
            'name' => 'Litecoin',
            'symbol' => 'LTC',
            'icon' => 'Ł',
            'min_amount' => 0.01
        ],
        'bch' => [
            'name' => 'Bitcoin Cash',
            'symbol' => 'BCH',
            'icon' => '₿',
            'min_amount' => 0.001
        ],
        'trx' => [
            'name' => 'Tron',
            'symbol' => 'TRX',
            'icon' => 'T',
            'min_amount' => 10
        ],
        'bnb' => [
            'name' => 'Binance Coin',
            'symbol' => 'BNB',
            'icon' => 'B',
            'min_amount' => 0.01
        ]
    ]
];
