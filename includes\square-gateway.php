<?php
/**
 * Square Payment Gateway Class
 * ELOH Processing - Square API Integration
 *
 * Ready for future activation when Square becomes available in Dominica
 * or for international customers requiring card payments
 */

class Square_Gateway {
    private $config;
    private $client;

    // Payment APIs
    private $paymentsApi;
    private $refundsApi;
    private $cardsApi;

    // Customer & Business APIs
    private $customersApi;
    private $loyaltyApi;
    private $giftCardsApi;
    private $giftCardActivitiesApi;

    // Catalog & Inventory APIs
    private $catalogApi;
    private $inventoryApi;
    private $ordersApi;

    // Business Management APIs
    private $invoicesApi;
    private $subscriptionsApi;
    private $bookingsApi;
    private $teamApi;
    private $laborApi;

    // Location & Merchant APIs
    private $locationsApi;
    private $merchantsApi;
    private $vendorsApi;

    // Financial APIs
    private $payoutsApi;
    private $bankAccountsApi;
    private $disputesApi;

    // Webhook & Events APIs
    private $webhookSubscriptionsApi;

    public function __construct() {
        $this->config = require_once 'square-config.php';
        $this->initializeClient();
    }

    /**
     * Initialize Square API client
     */
    private function initializeClient() {
        try {
            // Note: This requires Square PHP SDK to be installed
            // composer require square/square

            if (class_exists('Square\SquareClient')) {
                $environment = $this->config['environment'] === 'production'
                    ? \Square\Environment::PRODUCTION
                    : \Square\Environment::SANDBOX;

                $this->client = new \Square\SquareClient([
                    'accessToken' => $this->config['access_token'],
                    'environment' => $environment,
                    'customUrl' => '',
                    'squareVersion' => '2024-12-18' // Latest API version
                ]);

                // Initialize Payment APIs
                $this->paymentsApi = $this->client->getPaymentsApi();
                $this->refundsApi = $this->client->getRefundsApi();
                $this->cardsApi = $this->client->getCardsApi();

                // Initialize Customer & Business APIs
                $this->customersApi = $this->client->getCustomersApi();
                $this->loyaltyApi = $this->client->getLoyaltyApi();
                $this->giftCardsApi = $this->client->getGiftCardsApi();
                $this->giftCardActivitiesApi = $this->client->getGiftCardActivitiesApi();

                // Initialize Catalog & Inventory APIs
                $this->catalogApi = $this->client->getCatalogApi();
                $this->inventoryApi = $this->client->getInventoryApi();
                $this->ordersApi = $this->client->getOrdersApi();

                // Initialize Business Management APIs
                $this->invoicesApi = $this->client->getInvoicesApi();
                $this->subscriptionsApi = $this->client->getSubscriptionsApi();
                $this->bookingsApi = $this->client->getBookingsApi();
                $this->teamApi = $this->client->getTeamApi();
                $this->laborApi = $this->client->getLaborApi();

                // Initialize Location & Merchant APIs
                $this->locationsApi = $this->client->getLocationsApi();
                $this->merchantsApi = $this->client->getMerchantsApi();
                $this->vendorsApi = $this->client->getVendorsApi();

                // Initialize Financial APIs
                $this->payoutsApi = $this->client->getPayoutsApi();
                $this->bankAccountsApi = $this->client->getBankAccountsApi();
                $this->disputesApi = $this->client->getDisputesApi();

                // Initialize Webhook & Events APIs
                $this->webhookSubscriptionsApi = $this->client->getWebhookSubscriptionsApi();

                $this->log('Square client initialized successfully');
            } else {
                $this->log('Square SDK not installed - integration ready for future activation', 'warning');
            }
        } catch (Exception $e) {
            $this->log('Error initializing Square client: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * Check if Square is available and configured
     */
    public function isAvailable() {
        // Check if in supported region
        if (!$this->isRegionSupported()) {
            return false;
        }

        // Check if SDK is installed
        if (!class_exists('Square\SquareClient')) {
            return false;
        }

        // Check if credentials are configured
        if (empty($this->config['access_token']) ||
            strpos($this->config['access_token'], 'YOUR_') !== false) {
            return false;
        }

        return true;
    }

    /**
     * Check if current region supports Square
     */
    private function isRegionSupported() {
        // For now, only enable for international customers or when explicitly enabled
        return $this->config['regional_support']['international']['supported'];
    }

    /**
     * Get gateway information
     */
    public function getGatewayInfo() {
        return [
            'id' => 'square',
            'name' => 'Square',
            'description' => 'Credit cards, debit cards, and digital wallets',
            'icon' => '💳',
            'supported_currencies' => $this->config['supported_currencies'],
            'features' => [
                'Credit Cards (Visa, Mastercard, Amex, Discover)',
                'Digital Wallets (Apple Pay, Google Pay)',
                'ACH Bank Transfers',
                'Secure PCI Compliance',
                'Real-time Processing'
            ],
            'fees' => [
                'card_present' => '2.6% + 10¢',
                'card_not_present' => '2.9% + 30¢',
                'digital_wallet' => '2.6% + 10¢'
            ],
            'available' => $this->isAvailable(),
            'status' => $this->getStatus()
        ];
    }

    /**
     * Get current status
     */
    public function getStatus() {
        if (!$this->isRegionSupported()) {
            return 'Not available in Dominica - Ready for future activation';
        }

        if (!class_exists('Square\SquareClient')) {
            return 'SDK not installed - Run: composer require square/square';
        }

        if (empty($this->config['access_token']) ||
            strpos($this->config['access_token'], 'YOUR_') !== false) {
            return 'Credentials not configured';
        }

        return 'Ready for activation';
    }

    /**
     * Create payment intent
     */
    public function createPayment($amount, $currency, $paymentToken, $options = []) {
        if (!$this->isAvailable()) {
            throw new Exception($this->config['error_messages']['region_not_supported']);
        }

        try {
            $amountMoney = new \Square\Models\Money();
            $amountMoney->setAmount($amount * 100); // Convert to cents
            $amountMoney->setCurrency($currency);

            $createPaymentRequest = new \Square\Models\CreatePaymentRequest(
                $paymentToken,
                uniqid('eloh_'), // Idempotency key
                $amountMoney
            );

            // Set location ID
            $createPaymentRequest->setLocationId($this->config['location_id']);

            // Add customer ID if provided
            if (!empty($options['customer_id'])) {
                $createPaymentRequest->setCustomerId($options['customer_id']);
            }

            // Add order ID if provided
            if (!empty($options['order_id'])) {
                $createPaymentRequest->setOrderId($options['order_id']);
            }

            // Add note if provided
            if (!empty($options['note'])) {
                $createPaymentRequest->setNote($options['note']);
            }

            $response = $this->paymentsApi->createPayment($createPaymentRequest);

            if ($response->isSuccess()) {
                $payment = $response->getResult()->getPayment();

                $this->log('Payment created successfully: ' . $payment->getId());

                return [
                    'success' => true,
                    'payment_id' => $payment->getId(),
                    'status' => $payment->getStatus(),
                    'amount' => $payment->getAmountMoney()->getAmount() / 100,
                    'currency' => $payment->getAmountMoney()->getCurrency(),
                    'created_at' => $payment->getCreatedAt(),
                    'receipt_url' => $payment->getReceiptUrl()
                ];
            } else {
                $errors = $response->getErrors();
                $errorMessage = !empty($errors) ? $errors[0]->getDetail() : 'Unknown error';

                $this->log('Payment creation failed: ' . $errorMessage, 'error');

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'error_code' => !empty($errors) ? $errors[0]->getCode() : 'UNKNOWN'
                ];
            }
        } catch (Exception $e) {
            $this->log('Payment exception: ' . $e->getMessage(), 'error');

            return [
                'success' => false,
                'error' => $this->config['error_messages']['processing_error'],
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Create refund
     */
    public function createRefund($paymentId, $amount, $currency, $reason = '') {
        if (!$this->isAvailable()) {
            throw new Exception($this->config['error_messages']['region_not_supported']);
        }

        try {
            $amountMoney = new \Square\Models\Money();
            $amountMoney->setAmount($amount * 100); // Convert to cents
            $amountMoney->setCurrency($currency);

            $refundRequest = new \Square\Models\RefundPaymentRequest(
                uniqid('refund_'), // Idempotency key
                $amountMoney,
                $paymentId
            );

            if (!empty($reason)) {
                $refundRequest->setReason($reason);
            }

            $response = $this->refundsApi->refundPayment($refundRequest);

            if ($response->isSuccess()) {
                $refund = $response->getResult()->getRefund();

                $this->log('Refund created successfully: ' . $refund->getId());

                return [
                    'success' => true,
                    'refund_id' => $refund->getId(),
                    'status' => $refund->getStatus(),
                    'amount' => $refund->getAmountMoney()->getAmount() / 100,
                    'currency' => $refund->getAmountMoney()->getCurrency()
                ];
            } else {
                $errors = $response->getErrors();
                $errorMessage = !empty($errors) ? $errors[0]->getDetail() : 'Unknown error';

                $this->log('Refund creation failed: ' . $errorMessage, 'error');

                return [
                    'success' => false,
                    'error' => $errorMessage
                ];
            }
        } catch (Exception $e) {
            $this->log('Refund exception: ' . $e->getMessage(), 'error');

            return [
                'success' => false,
                'error' => $this->config['error_messages']['processing_error']
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus($paymentId) {
        if (!$this->isAvailable()) {
            return ['status' => 'unavailable'];
        }

        try {
            $response = $this->paymentsApi->getPayment($paymentId);

            if ($response->isSuccess()) {
                $payment = $response->getResult()->getPayment();

                return [
                    'success' => true,
                    'status' => $payment->getStatus(),
                    'amount' => $payment->getAmountMoney()->getAmount() / 100,
                    'currency' => $payment->getAmountMoney()->getCurrency()
                ];
            } else {
                return ['success' => false, 'error' => 'Payment not found'];
            }
        } catch (Exception $e) {
            $this->log('Get payment status exception: ' . $e->getMessage(), 'error');
            return ['success' => false, 'error' => 'Error retrieving payment status'];
        }
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook($body, $signature, $url) {
        if (empty($this->config['webhook_signature_key'])) {
            return false;
        }

        try {
            $expectedSignature = base64_encode(hash_hmac(
                'sha256',
                $url . $body,
                $this->config['webhook_signature_key'],
                true
            ));

            return hash_equals($expectedSignature, $signature);
        } catch (Exception $e) {
            $this->log('Webhook validation error: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Get Web Payments SDK configuration
     */
    public function getWebPaymentsConfig() {
        return [
            'applicationId' => $this->config['application_id'],
            'locationId' => $this->config['location_id'],
            'environment' => $this->config['web_payments_sdk']['environment'],
            'style' => $this->config['web_payments_sdk']['card_form_style']
        ];
    }

    /**
     * Log messages
     */
    private function log($message, $level = 'info') {
        if (!$this->config['logging']['enabled']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;

        // In a real implementation, you'd write to a log file
        if ($this->config['development']['debug_mode']) {
            error_log("Square Gateway: $logMessage");
        }
    }
}
?>
