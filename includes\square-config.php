<?php
/**
 * Square API Configuration
 * ELOH Processing - Square Payment Gateway Integration
 *
 * This configuration is ready for future activation when:
 * 1. Square expands to Dominica
 * 2. International customers need card payments
 * 3. Business requirements change
 */

return [
    // Environment Settings
    'environment' => 'sandbox', // 'sandbox' or 'production'
    'sandbox_mode' => true, // Set to false for production

    // API Credentials (Replace with your actual credentials)
    'application_id' => '*****************************', // Replace with actual app ID
    'access_token' => 'EAAAl7nkL7zdhHGThIoGJLKjgqlJe8p1hezVRtbEsvXkTOP5oEU7_-eBcqNPfiid', // Replace with actual token
    'webhook_signature_key' => 'iq5Ua-e_DsdAkpMT1IRpSg', // Replace with actual key
    'location_id' => 'LMTMJQE2N30GB', // Replace with actual location ID

    // Business Information
    'business_name' => 'ELOH Processing LLC',
    'business_country' => 'DM', // Dominica
    'business_currency' => 'USD',
    'business_timezone' => 'America/Dominica',

    // Supported Payment Methods
    'payment_methods' => [
        'card' => true,           // Credit/Debit cards
        'digital_wallet' => true, // Apple Pay, Google Pay, Samsung Pay
        'ach' => true,           // ACH bank transfers
        'gift_card' => true,     // Square gift cards
        'afterpay' => true,      // Buy now, pay later
        'cash_app' => true,      // Cash App Pay
        'cash' => true           // Cash payments
    ],

    // Square APIs Configuration
    'apis' => [
        'payments' => true,       // Payments API
        'refunds' => true,        // Refunds API
        'customers' => true,      // Customers API
        'catalog' => true,        // Catalog API (Products/Services)
        'inventory' => true,      // Inventory API
        'orders' => true,         // Orders API
        'invoices' => true,       // Invoices API
        'subscriptions' => true,  // Subscriptions API
        'bookings' => true,       // Bookings API
        'loyalty' => true,        // Loyalty API
        'gift_cards' => true,     // Gift Cards API
        'team' => true,          // Team API (Staff management)
        'locations' => true,      // Locations API
        'merchants' => true,      // Merchants API
        'disputes' => true,       // Disputes API
        'payouts' => true,        // Payouts API
        'bank_accounts' => true,  // Bank Accounts API
        'cards' => true,         // Cards API (Card on file)
        'terminal' => false,      // Terminal API (for physical terminals)
        'labor' => true,         // Labor API (Time tracking)
        'vendors' => true,        // Vendors API
        'webhook_subscriptions' => true // Webhook Subscriptions API
    ],

    // Supported Card Types
    'supported_cards' => [
        'visa' => true,
        'mastercard' => true,
        'american_express' => true,
        'discover' => true,
        'jcb' => false,          // Not common in Dominica
        'diners_club' => false   // Not common in Dominica
    ],

    // Currency Settings
    'currency' => 'USD',
    'supported_currencies' => ['USD'], // Only USD for now

    // Payment Limits (in cents)
    'minimum_amount' => 100,    // $1.00 USD minimum
    'maximum_amount' => 999999, // $9,999.99 USD maximum

    // Web Payments SDK Settings
    'web_payments_sdk' => [
        'version' => 'latest',
        'environment' => 'sandbox', // 'sandbox' or 'production'
        'card_form_style' => [
            'input' => [
                'fontSize' => '16px',
                'fontFamily' => 'Arial, sans-serif',
                'color' => '#333',
                'backgroundColor' => '#fff'
            ],
            'placeholder' => [
                'color' => '#999'
            ],
            'focus' => [
                'borderColor' => '#0077cc'
            ],
            'error' => [
                'borderColor' => '#dc3545',
                'color' => '#dc3545'
            ]
        ]
    ],

    // Webhook Settings
    'webhooks' => [
        'enabled' => true,
        'endpoint' => '/square-webhook.php',
        'events' => [
            'payment.created',
            'payment.updated',
            'refund.created',
            'refund.updated'
        ]
    ],

    // Security Settings
    'security' => [
        'verify_ssl' => true,
        'timeout' => 30,         // API timeout in seconds
        'max_retries' => 3,      // Maximum API retry attempts
        'rate_limit' => 100      // Requests per minute
    ],

    // Feature Flags
    'features' => [
        'save_cards' => true,         // Allow saving cards for future use
        'recurring_payments' => true, // Recurring payments/subscriptions
        'partial_payments' => true,   // Partial payments with gift cards
        'tips' => true,              // Tip functionality
        'receipts' => true,          // Email receipts
        'refunds' => true,           // Allow refunds
        'loyalty_program' => true,   // Customer loyalty program
        'gift_cards' => true,        // Gift card sales and redemption
        'inventory_tracking' => true, // Real-time inventory management
        'staff_management' => true,   // Employee/team management
        'appointment_booking' => true, // Service appointment booking
        'invoicing' => true,         // Invoice creation and management
        'reporting' => true,         // Sales and analytics reporting
        'multi_location' => false,   // Multiple business locations
        'vendor_management' => true, // Supplier/vendor tracking
        'time_tracking' => true     // Employee time tracking
    ],

    // Business Management Settings
    'business_management' => [
        'services' => [
            'crypto_consulting' => [
                'name' => 'Cryptocurrency & Forex Consulting',
                'price' => 150.00,
                'duration' => 60, // minutes
                'category' => 'consulting',
                'description' => 'Expert cryptocurrency and forex trading consultation',
                'bookable' => true
            ],
            'mining_pool' => [
                'name' => 'Mining Pool Membership',
                'price' => 200.00,
                'duration' => null, // annual membership
                'category' => 'membership',
                'description' => 'Annual mining pool membership with profit sharing',
                'bookable' => false
            ],
            'mining_services' => [
                'name' => 'Managed Mining Services',
                'price' => 500.00,
                'duration' => null, // monthly service
                'category' => 'service',
                'description' => 'Professional cryptocurrency mining management',
                'bookable' => false
            ],
            'market_analysis' => [
                'name' => 'Market Analysis Report',
                'price' => 99.00,
                'duration' => null, // one-time purchase
                'category' => 'report',
                'description' => 'Detailed cryptocurrency market analysis and recommendations',
                'bookable' => false
            ]
        ],
        'inventory' => [
            'track_services' => true,
            'track_digital_products' => true,
            'low_stock_alerts' => true,
            'auto_reorder' => false
        ],
        'staff' => [
            'roles' => ['admin', 'consultant', 'analyst', 'support'],
            'permissions' => [
                'admin' => ['all'],
                'consultant' => ['bookings', 'customers', 'payments'],
                'analyst' => ['reports', 'customers'],
                'support' => ['customers', 'bookings']
            ]
        ]
    ],

    // Integration Settings
    'integration' => [
        'name' => 'ELOH Processing Multi-Gateway',
        'version' => '1.0.0',
        'user_agent' => 'ELOH-Processing/1.0.0 Square-Integration',
        'callback_urls' => [
            'success' => '/payment-success.php',
            'cancel' => '/payment-cancelled.php',
            'error' => '/error.php'
        ]
    ],

    // Logging Settings
    'logging' => [
        'enabled' => true,
        'level' => 'info',        // 'debug', 'info', 'warning', 'error'
        'file' => 'logs/square.log',
        'max_size' => '10MB',
        'rotate' => true
    ],

    // Development Settings
    'development' => [
        'debug_mode' => true,     // Set to false in production
        'test_mode' => true,      // Use test data
        'mock_payments' => false, // Mock payment responses
        'verbose_logging' => true // Detailed logs
    ],

    // Regional Settings for Future Expansion
    'regional_support' => [
        'dominica' => [
            'supported' => false,    // Not yet available
            'currency' => 'USD',
            'tax_rate' => 0.15,     // 15% VAT in Dominica
            'local_payment_methods' => []
        ],
        'international' => [
            'supported' => true,     // For international customers
            'currencies' => ['USD'],
            'regions' => ['US', 'CA', 'GB', 'AU', 'JP', 'FR', 'ES']
        ]
    ],

    // Error Messages
    'error_messages' => [
        'card_declined' => 'Your card was declined. Please try a different payment method.',
        'insufficient_funds' => 'Insufficient funds. Please try a different card.',
        'invalid_card' => 'Please check your card information and try again.',
        'expired_card' => 'Your card has expired. Please use a different card.',
        'processing_error' => 'Payment processing error. Please try again.',
        'network_error' => 'Network error. Please check your connection and try again.',
        'service_unavailable' => 'Square payment service is temporarily unavailable.',
        'region_not_supported' => 'Square payments are not yet available in your region.'
    ],

    // Success Messages
    'success_messages' => [
        'payment_completed' => 'Payment completed successfully!',
        'card_saved' => 'Card saved for future payments.',
        'refund_processed' => 'Refund processed successfully.'
    ],

    // API Endpoints
    'api_endpoints' => [
        'sandbox' => [
            'base_url' => 'https://connect.squareupsandbox.com',
            'payments' => '/v2/payments',
            'refunds' => '/v2/refunds',
            'customers' => '/v2/customers',
            'cards' => '/v2/cards'
        ],
        'production' => [
            'base_url' => 'https://connect.squareup.com',
            'payments' => '/v2/payments',
            'refunds' => '/v2/refunds',
            'customers' => '/v2/customers',
            'cards' => '/v2/cards'
        ]
    ]
];
?>
