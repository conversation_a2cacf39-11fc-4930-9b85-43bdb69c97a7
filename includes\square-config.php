<?php
/**
 * Square API Configuration
 * ELOH Processing - Square Payment Gateway Integration
 * 
 * This configuration is ready for future activation when:
 * 1. Square expands to Dominica
 * 2. International customers need card payments
 * 3. Business requirements change
 */

return [
    // Environment Settings
    'environment' => 'sandbox', // 'sandbox' or 'production'
    'sandbox_mode' => true, // Set to false for production
    
    // API Credentials (Replace with your actual credentials)
    'application_id' => 'sandbox-sq0idb-YOUR_APPLICATION_ID', // Replace with actual app ID
    'access_token' => 'EAAAEL_YOUR_SANDBOX_ACCESS_TOKEN', // Replace with actual token
    'webhook_signature_key' => 'YOUR_WEBHOOK_SIGNATURE_KEY', // Replace with actual key
    'location_id' => 'YOUR_LOCATION_ID', // Replace with actual location ID
    
    // Business Information
    'business_name' => 'ELOH Processing LLC',
    'business_country' => 'DM', // Dominica
    'business_currency' => 'USD',
    'business_timezone' => 'America/Dominica',
    
    // Supported Payment Methods
    'payment_methods' => [
        'card' => true,           // Credit/Debit cards
        'digital_wallet' => true, // Apple Pay, Google Pay, Samsung Pay
        'ach' => true,           // ACH bank transfers
        'gift_card' => false,    // Square gift cards (disabled for now)
        'afterpay' => false,     // Buy now, pay later (disabled for now)
        'cash_app' => false      // Cash App Pay (disabled for now)
    ],
    
    // Supported Card Types
    'supported_cards' => [
        'visa' => true,
        'mastercard' => true,
        'american_express' => true,
        'discover' => true,
        'jcb' => false,          // Not common in Dominica
        'diners_club' => false   // Not common in Dominica
    ],
    
    // Currency Settings
    'currency' => 'USD',
    'supported_currencies' => ['USD'], // Only USD for now
    
    // Payment Limits (in cents)
    'minimum_amount' => 100,    // $1.00 USD minimum
    'maximum_amount' => 999999, // $9,999.99 USD maximum
    
    // Web Payments SDK Settings
    'web_payments_sdk' => [
        'version' => 'latest',
        'environment' => 'sandbox', // 'sandbox' or 'production'
        'card_form_style' => [
            'input' => [
                'fontSize' => '16px',
                'fontFamily' => 'Arial, sans-serif',
                'color' => '#333',
                'backgroundColor' => '#fff'
            ],
            'placeholder' => [
                'color' => '#999'
            ],
            'focus' => [
                'borderColor' => '#0077cc'
            ],
            'error' => [
                'borderColor' => '#dc3545',
                'color' => '#dc3545'
            ]
        ]
    ],
    
    // Webhook Settings
    'webhooks' => [
        'enabled' => true,
        'endpoint' => '/square-webhook.php',
        'events' => [
            'payment.created',
            'payment.updated',
            'refund.created',
            'refund.updated'
        ]
    ],
    
    // Security Settings
    'security' => [
        'verify_ssl' => true,
        'timeout' => 30,         // API timeout in seconds
        'max_retries' => 3,      // Maximum API retry attempts
        'rate_limit' => 100      // Requests per minute
    ],
    
    // Feature Flags
    'features' => [
        'save_cards' => true,     // Allow saving cards for future use
        'recurring_payments' => false, // Recurring payments (future feature)
        'partial_payments' => false,   // Partial payments (future feature)
        'tips' => false,              // Tip functionality (future feature)
        'receipts' => true,           // Email receipts
        'refunds' => true             // Allow refunds
    ],
    
    // Integration Settings
    'integration' => [
        'name' => 'ELOH Processing Multi-Gateway',
        'version' => '1.0.0',
        'user_agent' => 'ELOH-Processing/1.0.0 Square-Integration',
        'callback_urls' => [
            'success' => '/payment-success.php',
            'cancel' => '/payment-cancelled.php',
            'error' => '/error.php'
        ]
    ],
    
    // Logging Settings
    'logging' => [
        'enabled' => true,
        'level' => 'info',        // 'debug', 'info', 'warning', 'error'
        'file' => 'logs/square.log',
        'max_size' => '10MB',
        'rotate' => true
    ],
    
    // Development Settings
    'development' => [
        'debug_mode' => true,     // Set to false in production
        'test_mode' => true,      // Use test data
        'mock_payments' => false, // Mock payment responses
        'verbose_logging' => true // Detailed logs
    ],
    
    // Regional Settings for Future Expansion
    'regional_support' => [
        'dominica' => [
            'supported' => false,    // Not yet available
            'currency' => 'USD',
            'tax_rate' => 0.15,     // 15% VAT in Dominica
            'local_payment_methods' => []
        ],
        'international' => [
            'supported' => true,     // For international customers
            'currencies' => ['USD'],
            'regions' => ['US', 'CA', 'GB', 'AU', 'JP', 'FR', 'ES']
        ]
    ],
    
    // Error Messages
    'error_messages' => [
        'card_declined' => 'Your card was declined. Please try a different payment method.',
        'insufficient_funds' => 'Insufficient funds. Please try a different card.',
        'invalid_card' => 'Please check your card information and try again.',
        'expired_card' => 'Your card has expired. Please use a different card.',
        'processing_error' => 'Payment processing error. Please try again.',
        'network_error' => 'Network error. Please check your connection and try again.',
        'service_unavailable' => 'Square payment service is temporarily unavailable.',
        'region_not_supported' => 'Square payments are not yet available in your region.'
    ],
    
    // Success Messages
    'success_messages' => [
        'payment_completed' => 'Payment completed successfully!',
        'card_saved' => 'Card saved for future payments.',
        'refund_processed' => 'Refund processed successfully.'
    ],
    
    // API Endpoints
    'api_endpoints' => [
        'sandbox' => [
            'base_url' => 'https://connect.squareupsandbox.com',
            'payments' => '/v2/payments',
            'refunds' => '/v2/refunds',
            'customers' => '/v2/customers',
            'cards' => '/v2/cards'
        ],
        'production' => [
            'base_url' => 'https://connect.squareup.com',
            'payments' => '/v2/payments',
            'refunds' => '/v2/refunds',
            'customers' => '/v2/customers',
            'cards' => '/v2/cards'
        ]
    ]
];
?>
