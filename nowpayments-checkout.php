<?php
session_start();
require_once "includes/nowpayments-gateway.php";
include "header.php";

$payment_id = $_GET["payment_id"] ?? "";
$order_id = $_GET["order"] ?? "";

if (!$payment_id || !$order_id) {
    header("Location: index.php");
    exit;
}

// Get payment data from session
$payment_data = $_SESSION['multi_gateway_payments'][$order_id] ?? null;

if (!$payment_data) {
    echo "<p>Payment session not found.</p>";
    include "footer.php";
    exit;
}

$gateway = new NowPayments_Gateway(true); // Sandbox mode
$payment_info = $payment_data['payment_data'];
?>

<main>
  <section class="hero">
    <h1>Complete Your Cryptocurrency Payment</h1>
  </section>

  <section class="section" id="payment-details">
    <div style="max-width: 800px; margin: 0 auto;">
      <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>🌐 NowPayments Checkout</h3>
        <p>Secure cryptocurrency payment processing with support for 300+ digital currencies.</p>
      </div>
      
      <div style="background: #f9f9f9; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h2>Payment Details</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <p><strong>Description:</strong> <?php echo htmlspecialchars($payment_data['description']); ?></p>
            <p><strong>Amount:</strong> $<?php echo number_format($payment_data['amount'], 2); ?> USD</p>
            <p><strong>Payment ID:</strong> <?php echo htmlspecialchars($payment_id); ?></p>
            <p><strong>Order ID:</strong> <?php echo htmlspecialchars($order_id); ?></p>
          </div>
          <div>
            <p><strong>Cryptocurrency:</strong> <?php echo strtoupper($payment_data['currency']); ?></p>
            <p><strong>Amount to Send:</strong> <?php echo $payment_info['pay_amount']; ?> <?php echo strtoupper($payment_info['pay_currency']); ?></p>
            <p><strong>Status:</strong> <span id="payment-status"><?php echo ucfirst($payment_info['payment_status']); ?></span></p>
            <p><strong>Email:</strong> <?php echo htmlspecialchars($payment_data['email']); ?></p>
          </div>
        </div>
      </div>

      <div class="two-col">
        <div class="col">
          <h3>Send Payment To:</h3>
          <div style="background: #fff; padding: 15px; border: 2px solid #0077cc; border-radius: 5px; word-break: break-all; margin-bottom: 15px;">
            <strong><?php echo $payment_info['pay_address']; ?></strong>
          </div>
          <button onclick="copyToClipboard('<?php echo $payment_info['pay_address']; ?>')" 
                  style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">
            📋 Copy Address
          </button>
          <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
            Send exactly <strong><?php echo $payment_info['pay_amount']; ?> <?php echo strtoupper($payment_info['pay_currency']); ?></strong> to the address above
          </p>
        </div>
        
        <div class="col" style="text-align: center;">
          <h3>QR Code:</h3>
          <img src="<?php echo $payment_info['qr_code_url']; ?>" 
               alt="Payment QR Code" style="border: 1px solid #ddd; border-radius: 5px; max-width: 200px;">
          <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
            Scan with your crypto wallet
          </p>
        </div>
      </div>

      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 30px 0;">
        <h3>⚠️ Important Instructions:</h3>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Send exactly <strong><?php echo $payment_info['pay_amount']; ?> <?php echo strtoupper($payment_info['pay_currency']); ?></strong> to the address above</li>
          <li>Do not send any other cryptocurrency to this address</li>
          <li>Payment confirmation may take 10-60 minutes depending on network congestion</li>
          <li>This payment will expire in 1 hour if not completed</li>
          <li>Keep this page open to monitor payment status</li>
        </ul>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <button onclick="checkPaymentStatus()" class="cta-button" style="margin-right: 10px;">
          🔄 Check Payment Status
        </button>
        <a href="contact.php" class="cta-button" style="background: #6c757d;">
          Need Help?
        </a>
      </div>

      <div id="payment-status-result" style="margin-top: 20px; text-align: center;"></div>
      
      <!-- Payment Progress -->
      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 30px;">
        <h3>Payment Progress:</h3>
        <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
          <div style="text-align: center; flex: 1;">
            <div style="width: 30px; height: 30px; border-radius: 50%; background: #28a745; color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">✓</div>
            <small>Payment Created</small>
          </div>
          <div style="flex: 1; height: 2px; background: #ddd; margin: 0 10px;"></div>
          <div style="text-align: center; flex: 1;">
            <div id="step-waiting" style="width: 30px; height: 30px; border-radius: 50%; background: #ffc107; color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">⏳</div>
            <small>Waiting for Payment</small>
          </div>
          <div style="flex: 1; height: 2px; background: #ddd; margin: 0 10px;"></div>
          <div style="text-align: center; flex: 1;">
            <div id="step-confirmed" style="width: 30px; height: 30px; border-radius: 50%; background: #ddd; color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">3</div>
            <small>Payment Confirmed</small>
          </div>
        </div>
      </div>
    </div>
  </section>
</main>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Address copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            alert('Address copied to clipboard!');
        } catch (err) {
            alert('Failed to copy address. Please copy manually.');
        }
        document.body.removeChild(textArea);
    });
}

function checkPaymentStatus() {
    const statusDiv = document.getElementById("payment-status-result");
    const statusSpan = document.getElementById("payment-status");
    
    statusDiv.innerHTML = "<p>Checking payment status...</p>";
    
    fetch("nowpayments-status.php?payment_id=<?php echo $payment_id; ?>")
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const status = data.payment_status;
                statusSpan.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                
                switch (status) {
                    case 'finished':
                    case 'confirmed':
                        statusDiv.innerHTML = "<div style=\"background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;\"><strong>✅ Payment Confirmed!</strong><br>Your payment has been successfully processed.</div>";
                        document.getElementById('step-confirmed').style.background = '#28a745';
                        document.getElementById('step-confirmed').innerHTML = '✓';
                        setTimeout(() => {
                            window.location.href = "payment-success.php?order=<?php echo $order_id; ?>&gateway=nowpayments";
                        }, 3000);
                        break;
                        
                    case 'partially_paid':
                        statusDiv.innerHTML = "<div style=\"background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px;\"><strong>⚠️ Partial Payment</strong><br>We received a partial payment. Please send the remaining amount.</div>";
                        break;
                        
                    case 'waiting':
                        statusDiv.innerHTML = "<div style=\"background: #cce5ff; color: #004085; padding: 15px; border-radius: 5px;\"><strong>⏳ Waiting for Payment</strong><br>Please send the cryptocurrency to complete your payment.</div>";
                        break;
                        
                    case 'expired':
                        statusDiv.innerHTML = "<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><strong>❌ Payment Expired</strong><br>This payment has expired. Please create a new payment.</div>";
                        break;
                        
                    default:
                        statusDiv.innerHTML = "<div style=\"background: #e2e3e5; color: #383d41; padding: 15px; border-radius: 5px;\"><strong>Status:</strong> " + status + "</div>";
                }
            } else {
                statusDiv.innerHTML = "<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><strong>❌ Error</strong><br>Could not check payment status.</div>";
            }
        })
        .catch(error => {
            statusDiv.innerHTML = "<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><strong>Error checking payment status</strong></div>";
        });
}

// Auto-check payment status every 30 seconds
setInterval(checkPaymentStatus, 30000);

// Initial status check after 5 seconds
setTimeout(checkPaymentStatus, 5000);
</script>

<?php include "footer.php"; ?>
