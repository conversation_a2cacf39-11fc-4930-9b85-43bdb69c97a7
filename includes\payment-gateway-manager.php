<?php
/**
 * Payment Gateway Manager for ELOH Processing
 * Manages multiple payment gateways (BTCPay Server, NowPayments)
 */

require_once __DIR__ . '/btcpay-gateway.php';
require_once __DIR__ . '/nowpayments-gateway.php';
require_once __DIR__ . '/square-gateway.php';

class Payment_Gateway_Manager {

    private $availableGateways;

    public function __construct() {
        $this->availableGateways = [
            'btcpay' => [
                'name' => 'BTCPay Server',
                'description' => 'Self-hosted Bitcoin payments with Lightning Network support',
                'supported_currencies' => ['BTC'],
                'features' => ['Lightning Network', 'On-chain Bitcoin', 'Self-hosted', 'No fees'],
                'icon' => '⚡',
                'class' => 'BTCPay_Gateway'
            ],
            'nowpayments' => [
                'name' => 'NowPayments',
                'description' => 'Accept 300+ cryptocurrencies with low fees',
                'supported_currencies' => ['BTC', 'ETH', 'USDT', 'USDC', 'LTC', 'BCH', 'TRX', 'BNB', 'ADA', 'DOT', 'XRP', 'MATIC'],
                'features' => ['300+ Cryptocurrencies', 'Low fees (0.5-1.5%)', 'Easy integration', 'Global support'],
                'icon' => '🌐',
                'class' => 'NowPayments_Gateway'
            ]
        ];
    }

    /**
     * Get available payment gateways
     */
    public function getAvailableGateways() {
        return $this->availableGateways;
    }

    /**
     * Get gateway instance
     */
    public function getGateway($gatewayId) {
        if (!isset($this->availableGateways[$gatewayId])) {
            throw new Exception("Gateway '$gatewayId' not found");
        }

        $gatewayClass = $this->availableGateways[$gatewayId]['class'];

        switch ($gatewayId) {
            case 'btcpay':
                return new BTCPay_Gateway();
            case 'nowpayments':
                return new NowPayments_Gateway(false); // Live mode
            default:
                throw new Exception("Gateway '$gatewayId' not implemented");
        }
    }

    /**
     * Create payment with specified gateway
     */
    public function createPayment($gatewayId, $amount, $currency, $orderId, $description = null, $customerEmail = null) {
        try {
            $gateway = $this->getGateway($gatewayId);

            switch ($gatewayId) {
                case 'btcpay':
                    return $gateway->createInvoice($amount, 'USD', $orderId, $customerEmail, $description);

                case 'nowpayments':
                    return $gateway->createPayment($amount, $currency, $orderId, $description, $customerEmail);

                default:
                    return [
                        'success' => false,
                        'error' => "Payment creation not implemented for gateway '$gatewayId'"
                    ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get payment status from specified gateway
     */
    public function getPaymentStatus($gatewayId, $paymentId) {
        try {
            $gateway = $this->getGateway($gatewayId);

            switch ($gatewayId) {
                case 'btcpay':
                    return $gateway->getInvoice($paymentId);

                case 'nowpayments':
                    return $gateway->getPaymentStatus($paymentId);

                default:
                    return [
                        'success' => false,
                        'error' => "Status check not implemented for gateway '$gatewayId'"
                    ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get supported currencies for a gateway
     */
    public function getSupportedCurrencies($gatewayId) {
        if (!isset($this->availableGateways[$gatewayId])) {
            return [];
        }

        return $this->availableGateways[$gatewayId]['supported_currencies'];
    }

    /**
     * Get all supported currencies across all gateways
     */
    public function getAllSupportedCurrencies() {
        $allCurrencies = [];

        foreach ($this->availableGateways as $gatewayId => $gateway) {
            foreach ($gateway['supported_currencies'] as $currency) {
                if (!in_array($currency, $allCurrencies)) {
                    $allCurrencies[] = $currency;
                }
            }
        }

        return $allCurrencies;
    }

    /**
     * Get recommended gateway for a currency
     */
    public function getRecommendedGateway($currency) {
        $currency = strtoupper($currency);

        // BTCPay Server for Bitcoin (self-hosted, no fees)
        if ($currency === 'BTC') {
            return 'btcpay';
        }

        // NowPayments for other cryptocurrencies
        foreach ($this->availableGateways['nowpayments']['supported_currencies'] as $supportedCurrency) {
            if (strtoupper($supportedCurrency) === $currency) {
                return 'nowpayments';
            }
        }

        // Default to BTCPay if currency not found
        return 'btcpay';
    }

    /**
     * Get gateway comparison data
     */
    public function getGatewayComparison() {
        return [
            'btcpay' => [
                'pros' => [
                    'No transaction fees',
                    'Self-hosted (full control)',
                    'Lightning Network support',
                    'Privacy-focused',
                    'Open source'
                ],
                'cons' => [
                    'Bitcoin only',
                    'Requires technical setup',
                    'Self-managed'
                ],
                'best_for' => 'Bitcoin payments, privacy, no fees'
            ],
            'nowpayments' => [
                'pros' => [
                    '300+ cryptocurrencies',
                    'Low fees (0.5-1.5%)',
                    'Easy integration',
                    'Global support',
                    'No KYC required'
                ],
                'cons' => [
                    'Transaction fees apply',
                    'Third-party service',
                    'No sandbox for some regions'
                ],
                'best_for' => 'Multiple cryptocurrencies, global reach'
            ]
        ];
    }

    /**
     * Validate gateway configuration
     */
    public function validateGatewayConfig($gatewayId) {
        try {
            $gateway = $this->getGateway($gatewayId);

            if (method_exists($gateway, 'getDebugInfo')) {
                return $gateway->getDebugInfo();
            }

            return ['status' => 'Gateway loaded successfully'];

        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'status' => 'Configuration error'
            ];
        }
    }
}
