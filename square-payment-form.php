<?php
require_once "includes/square-gateway.php";
include "header.php";

// Get payment parameters
$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";
$preset_service = $_GET["service"] ?? "";

// Initialize Square gateway to get config
$squareGateway = new Square_Gateway();
$squareConfig = $squareGateway->getWebPaymentsConfig();
?>

<main>
  <section class="hero">
    <h1><?php echo $is_donation ? "Make a Donation with Square" : "Pay with Credit Card"; ?></h1>
  </section>

  <section class="section" id="square-payment-form">
    <div style="max-width: 600px; margin: 0 auto;">
      
      <!-- Payment Summary -->
      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>💳 Square Payment</h3>
        <p>Secure credit card processing powered by Square. Your payment information is encrypted and PCI compliant.</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h4>💳 Accepted Cards</h4>
            <p style="font-size: 1.5em; margin: 10px 0;">💳 💰 🍎 🤖</p>
            <small>Visa, Mastercard, Amex, Discover, Apple Pay, Google Pay</small>
          </div>
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h4>🔒 Security</h4>
            <p style="font-size: 1.5em; margin: 10px 0;">🛡️ 🔐</p>
            <small>PCI DSS Compliant, 256-bit SSL Encryption</small>
          </div>
        </div>
      </div>

      <!-- Payment Form -->
      <form id="payment-form" style="background: white; padding: 30px; border-radius: 10px; border: 1px solid #ddd;">
        
        <!-- Payment Details -->
        <div style="margin-bottom: 25px;">
          <h3>Payment Details</h3>
          
          <div style="margin-bottom: 20px;">
            <label for="amount" style="display: block; margin-bottom: 5px; font-weight: bold;">Amount (USD):</label>
            <input type="number" id="amount" name="amount" min="1" step="0.01" required
                   value="<?php echo htmlspecialchars($preset_amount ?: '10'); ?>"
                   style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px; font-size: 1.1em;">
            <small style="color: #666;">Minimum: $1.00 USD</small>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
            <input type="email" id="email" name="email" required
                   style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;"
                   placeholder="<EMAIL>">
            <small style="color: #666;">For payment confirmation and receipt</small>
          </div>

          <?php if (!$is_donation): ?>
          <div style="margin-bottom: 20px;">
            <label for="service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
            <select id="service" name="service" required
                    style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;">
              <option value="consulting" <?php echo ($preset_service === 'consulting') ? 'selected' : ''; ?>>Crypto & Forex Consulting ($150/hour)</option>
              <option value="mining-pool" <?php echo ($preset_service === 'mining-pool') ? 'selected' : ''; ?>>Mining Pool Membership ($200/year)</option>
              <option value="mining-services" <?php echo ($preset_service === 'mining-services') ? 'selected' : ''; ?>>Mining Services ($500/month)</option>
              <option value="analysis" <?php echo ($preset_service === 'analysis') ? 'selected' : ''; ?>>Market Analysis Report ($99/report)</option>
              <option value="other" <?php echo ($preset_service === 'other') ? 'selected' : ''; ?>>Other Services</option>
            </select>
          </div>
          <?php endif; ?>

          <div style="margin-bottom: 25px;">
            <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
            <textarea id="description" name="description" rows="3"
                      style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;"
                      placeholder="<?php echo $is_donation ? "Optional message with your donation" : "Describe the service you are paying for"; ?>"></textarea>
          </div>
        </div>

        <!-- Card Payment Section -->
        <div style="margin-bottom: 25px;">
          <h3>Card Information</h3>
          
          <!-- Square Web Payments SDK will inject card form here -->
          <div id="card-container" style="margin-bottom: 20px;"></div>
          
          <!-- Digital Wallet Buttons -->
          <div style="margin-bottom: 20px;">
            <div id="apple-pay-button" style="margin-bottom: 10px;"></div>
            <div id="google-pay-button" style="margin-bottom: 10px;"></div>
          </div>
        </div>

        <!-- Payment Button -->
        <div style="text-align: center; margin: 30px 0;">
          <button id="card-button" type="button" class="cta-button" 
                  style="padding: 15px 40px; font-size: 1.1em; width: 100%;" disabled>
            🔒 Processing...
          </button>
        </div>

        <!-- Loading/Error Messages -->
        <div id="payment-status" style="margin-top: 20px; text-align: center;"></div>
      </form>

      <!-- Security Information -->
      <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-top: 30px;">
        <h4>🔒 Your Security is Our Priority</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
          <div>
            <strong>🛡️ PCI DSS Compliant:</strong>
            <p>All card data is processed securely by Square and never stored on our servers.</p>
          </div>
          <div>
            <strong>🔐 256-bit SSL Encryption:</strong>
            <p>Your payment information is encrypted during transmission using bank-level security.</p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="multi-gateway-payment-form.php" style="color: #0077cc; text-decoration: none;">← Back to Payment Options</a> |
        <a href="index.php" style="color: #0077cc; text-decoration: none;">Homepage</a>
      </div>
    </div>
  </section>
</main>

<!-- Square Web Payments SDK -->
<script type="text/javascript" src="https://sandbox.web.squarecdn.com/v1/square.js"></script>

<script>
console.log('Square Payment Form loaded');

// Square configuration
const squareConfig = {
  applicationId: '<?php echo $squareConfig['applicationId']; ?>',
  locationId: '<?php echo $squareConfig['locationId']; ?>',
  environment: '<?php echo $squareConfig['environment']; ?>'
};

console.log('Square Config:', squareConfig);

let payments;
let card;
let applePay;
let googlePay;

// Initialize Square Web Payments SDK
async function initializeSquare() {
  try {
    if (!window.Square) {
      throw new Error('Square.js failed to load properly');
    }

    payments = window.Square.payments(squareConfig.applicationId, squareConfig.locationId);
    console.log('Square payments initialized');

    // Initialize card payment method
    await initializeCard();
    
    // Initialize digital wallets
    await initializeApplePay();
    await initializeGooglePay();

  } catch (error) {
    console.error('Failed to initialize Square:', error);
    showError('Failed to load payment system. Please refresh the page and try again.');
  }
}

// Initialize card payment
async function initializeCard() {
  try {
    card = await payments.card({
      style: <?php echo json_encode($squareConfig['style']); ?>
    });
    
    await card.attach('#card-container');
    console.log('Card payment method initialized');
    
    // Enable payment button
    const cardButton = document.getElementById('card-button');
    cardButton.disabled = false;
    cardButton.textContent = '💳 Pay with Card';
    cardButton.onclick = handleCardPayment;
    
  } catch (error) {
    console.error('Failed to initialize card payment:', error);
    showError('Card payment unavailable. Please try again later.');
  }
}

// Initialize Apple Pay
async function initializeApplePay() {
  try {
    const paymentRequest = buildPaymentRequest();
    applePay = await payments.applePay(paymentRequest);
    
    const applePayButton = document.getElementById('apple-pay-button');
    applePayButton.innerHTML = '<button style="width: 100%; padding: 12px; background: #000; color: white; border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">🍎 Pay with Apple Pay</button>';
    applePayButton.onclick = handleApplePayment;
    
    console.log('Apple Pay initialized');
  } catch (error) {
    console.log('Apple Pay not available:', error.message);
    document.getElementById('apple-pay-button').style.display = 'none';
  }
}

// Initialize Google Pay
async function initializeGooglePay() {
  try {
    const paymentRequest = buildPaymentRequest();
    googlePay = await payments.googlePay(paymentRequest);
    
    const googlePayButton = document.getElementById('google-pay-button');
    googlePayButton.innerHTML = '<button style="width: 100%; padding: 12px; background: #4285f4; color: white; border: none; border-radius: 5px; font-size: 1em; cursor: pointer;">🤖 Pay with Google Pay</button>';
    googlePayButton.onclick = handleGooglePayment;
    
    console.log('Google Pay initialized');
  } catch (error) {
    console.log('Google Pay not available:', error.message);
    document.getElementById('google-pay-button').style.display = 'none';
  }
}

// Build payment request for digital wallets
function buildPaymentRequest() {
  const amount = document.getElementById('amount').value;
  return {
    countryCode: 'US',
    currencyCode: 'USD',
    total: {
      amount: amount,
      label: 'ELOH Processing Payment'
    }
  };
}

// Handle card payment
async function handleCardPayment() {
  try {
    showLoading('Processing card payment...');
    
    const result = await card.tokenize();
    if (result.status === 'OK') {
      console.log('Card tokenized successfully:', result.token);
      await processPayment(result.token, 'card');
    } else {
      console.error('Card tokenization failed:', result.errors);
      showError('Card payment failed. Please check your card information and try again.');
    }
  } catch (error) {
    console.error('Card payment error:', error);
    showError('Payment processing error. Please try again.');
  }
}

// Handle Apple Pay
async function handleApplePayment() {
  try {
    showLoading('Processing Apple Pay...');
    
    const result = await applePay.tokenize();
    if (result.status === 'OK') {
      console.log('Apple Pay tokenized successfully:', result.token);
      await processPayment(result.token, 'apple_pay');
    } else {
      console.error('Apple Pay failed:', result.errors);
      showError('Apple Pay failed. Please try again.');
    }
  } catch (error) {
    console.error('Apple Pay error:', error);
    showError('Apple Pay processing error. Please try again.');
  }
}

// Handle Google Pay
async function handleGooglePayment() {
  try {
    showLoading('Processing Google Pay...');
    
    const result = await googlePay.tokenize();
    if (result.status === 'OK') {
      console.log('Google Pay tokenized successfully:', result.token);
      await processPayment(result.token, 'google_pay');
    } else {
      console.error('Google Pay failed:', result.errors);
      showError('Google Pay failed. Please try again.');
    }
  } catch (error) {
    console.error('Google Pay error:', error);
    showError('Google Pay processing error. Please try again.');
  }
}

// Process payment with backend
async function processPayment(token, paymentMethod) {
  try {
    const formData = new FormData();
    formData.append('payment_token', token);
    formData.append('payment_method', paymentMethod);
    formData.append('amount', document.getElementById('amount').value);
    formData.append('email', document.getElementById('email').value);
    formData.append('service', document.getElementById('service')?.value || '');
    formData.append('description', document.getElementById('description').value);
    formData.append('payment_type', '<?php echo $payment_type; ?>');
    
    const response = await fetch('square-process-payment.php', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      showSuccess('Payment successful! Redirecting...');
      setTimeout(() => {
        window.location.href = 'payment-success.php?payment_id=' + result.payment_id;
      }, 2000);
    } else {
      showError('Payment failed: ' + result.error);
    }
    
  } catch (error) {
    console.error('Payment processing error:', error);
    showError('Payment processing failed. Please try again.');
  }
}

// UI helper functions
function showLoading(message) {
  const status = document.getElementById('payment-status');
  status.innerHTML = `<div style="color: #0077cc; padding: 15px; background: #e7f3ff; border-radius: 5px;">${message}</div>`;
}

function showSuccess(message) {
  const status = document.getElementById('payment-status');
  status.innerHTML = `<div style="color: #155724; padding: 15px; background: #d4edda; border-radius: 5px;">${message}</div>`;
}

function showError(message) {
  const status = document.getElementById('payment-status');
  status.innerHTML = `<div style="color: #721c24; padding: 15px; background: #f8d7da; border-radius: 5px;">${message}</div>`;
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initializeSquare);
</script>

<?php include "footer.php"; ?>
