# BTCPay Server Integration Setup Guide
## ELOH Processing LLC Website

### 🎉 What's Been Created

Your website now has a complete BTCPay Server integration with the following new files:

#### **Core BTCPay Files:**
- `includes/btcpay-config.php` - Configuration settings
- `includes/btcpay-gateway.php` - BTCPay Server API client
- `btcpay-payment-form.php` - Bitcoin payment form
- `btcpay-process-payment.php` - Payment processor
- `btcpay-webhook.php` - Webhook handler for payment notifications
- `btcpay-test.php` - Test and setup page

#### **Backup Created:**
- `C:\Users\<USER>\Documents\ELOH_Processing_Backup_20250524_182901`

### 🔧 Setup Instructions

#### **Step 1: Configure BTCPay Server Settings**

Edit `includes/btcpay-config.php` and update:

```php
'host' => 'https://your-btcpay-server.com', // Your BTCPay Server URL
'api_key' => 'your_api_key_here', // Your API key
'store_id' => 'your_store_id_here', // Your Store ID
'webhook_secret' => 'your_webhook_secret_here', // Random secure string
```

#### **Step 2: Get Your BTCPay Server Information**

1. **BTCPay Server URL**: Your BTCPay Server instance URL
2. **Store ID**: Found in BTCPay Server → Stores → [Your Store] → Settings → General
3. **API Key**: Create in BTCPay Server → Account → API Keys with these permissions:
   - ✅ btcpay.store.canviewinvoices
   - ✅ btcpay.store.cancreateinvoice
   - ✅ btcpay.store.canmodifyinvoices
   - ✅ btcpay.store.webhooks.canmodifywebhooks
   - ✅ btcpay.store.canmodifystoresettings
   - ✅ btcpay.store.canviewstoresettings

#### **Step 3: Test the Integration**

1. Upload all files to your InfinityFree hosting
2. Visit: `yoursite.infinityfreeapp.com/btcpay-test.php`
3. Follow the test steps to verify configuration
4. Create a test invoice to confirm everything works

#### **Step 4: Update Your Website Links**

Replace old payment links with BTCPay versions:

**Old Links:**
- `payment-form.php?type=donation` 
- `payment-form.php?type=service`

**New Links:**
- `btcpay-payment-form.php?type=donation`
- `btcpay-payment-form.php?type=service`

### 🚀 Features

#### **Payment Methods:**
- ⚡ **Lightning Network**: Instant, low-fee payments
- 🔗 **On-Chain Bitcoin**: Traditional Bitcoin transactions
- 🔒 **Self-Hosted**: No third parties, direct to your wallet

#### **Payment Flow:**
1. Customer fills out payment form
2. BTCPay Server creates invoice
3. Customer redirected to BTCPay checkout
4. Customer pays with Bitcoin/Lightning
5. Webhook notifies your site of payment status
6. Customer redirected back to success page

#### **Webhook Events Handled:**
- Invoice Created
- Payment Received
- Payment Confirmed
- Payment Settled
- Invoice Expired
- Invalid Payment

### 🔒 Security Features

- ✅ Webhook signature validation
- ✅ API key authentication
- ✅ HTTPS required for production
- ✅ No sensitive data stored locally
- ✅ Direct Bitcoin payments to your wallet

### 📧 Email Notifications

The system logs email notifications for:
- Payment received
- Payment confirmed
- Payment completed

To enable actual email sending, integrate with:
- SendGrid
- Mailgun
- SMTP service

### 🐛 Troubleshooting

#### **Common Issues:**

1. **"API key not configured"**
   - Update `btcpay-config.php` with your API key

2. **"Invoice creation failed"**
   - Check API key permissions
   - Verify BTCPay Server URL is correct
   - Check BTCPay Server is accessible

3. **"Webhook validation failed"**
   - Ensure webhook secret matches in config
   - Check webhook URL is accessible

#### **Debug Steps:**
1. Visit `btcpay-test.php` for diagnostics
2. Check error logs in your hosting control panel
3. Verify BTCPay Server API permissions

### 🔄 Migration from Old System

Your old payment system files are still available:
- `payment-form.php` (original)
- `payment.php` (original)
- `process-payment.php` (original)

You can gradually migrate by updating links to use the BTCPay versions.

### 📊 Payment Tracking

Payment logs are stored in:
- `data/payment_logs.json` - Payment status updates
- Server error logs - Detailed debugging information

### 🎯 Next Steps

1. **Configure BTCPay Server** with your settings
2. **Test thoroughly** with small amounts
3. **Update website links** to use BTCPay forms
4. **Set up email notifications** for production
5. **Monitor payment logs** for any issues

### 💡 Benefits of BTCPay Server

- **No Fees**: Direct Bitcoin payments to your wallet
- **Privacy**: No KYC or account requirements
- **Control**: You own the payment infrastructure
- **Lightning**: Instant payments with minimal fees
- **Open Source**: Transparent and auditable code

Your ELOH Processing website now has professional Bitcoin payment processing! 🚀
