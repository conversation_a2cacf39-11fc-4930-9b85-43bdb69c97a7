<?php
require_once "includes/square-business-manager.php";
include "header.php";

$businessManager = new Square_Business_Manager();
$customers = $businessManager->searchCustomers();
?>

<main>
  <section class="hero">
    <h1>Square Customer Management</h1>
  </section>

  <section class="section">
    <div style="max-width: 1200px; margin: 0 auto;">
      
      <!-- Customer Search & Add -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <div style="display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: end;">
          <div>
            <h2>🔍 Customer Search</h2>
            <input type="text" id="customerSearch" placeholder="Search by name, email, or phone..."
                   style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px; font-size: 1em;">
          </div>
          <div>
            <button onclick="showAddCustomerForm()" class="cta-button" style="padding: 12px 24px;">
              ➕ Add New Customer
            </button>
          </div>
        </div>
      </div>

      <!-- Add Customer Form (Hidden by default) -->
      <div id="addCustomerForm" style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd; display: none;">
        <h2>👤 Add New Customer</h2>
        <form id="customerForm" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div>
            <label for="firstName" style="display: block; margin-bottom: 5px; font-weight: bold;">First Name:</label>
            <input type="text" id="firstName" name="firstName" required
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          </div>
          
          <div>
            <label for="lastName" style="display: block; margin-bottom: 5px; font-weight: bold;">Last Name:</label>
            <input type="text" id="lastName" name="lastName" required
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          </div>
          
          <div>
            <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
            <input type="email" id="email" name="email" required
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          </div>
          
          <div>
            <label for="phone" style="display: block; margin-bottom: 5px; font-weight: bold;">Phone:</label>
            <input type="tel" id="phone" name="phone"
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          </div>
          
          <div style="grid-column: 1 / -1; display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
            <button type="submit" class="cta-button" style="padding: 10px 20px;">
              ✅ Create Customer
            </button>
            <button type="button" onclick="hideAddCustomerForm()" 
                    style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
              ❌ Cancel
            </button>
          </div>
        </form>
      </div>

      <!-- Customer List -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>👥 Customer Database</h2>
        
        <?php if ($customers['success']): ?>
          <?php if (empty($customers['customers'])): ?>
          <div style="text-align: center; padding: 40px; color: #666;">
            <div style="font-size: 3em; margin-bottom: 20px;">👥</div>
            <h3>No Customers Found</h3>
            <p>Start by adding your first customer or connecting to Square to sync existing customers.</p>
            <button onclick="showAddCustomerForm()" class="cta-button" style="margin-top: 15px;">
              ➕ Add First Customer
            </button>
          </div>
          <?php else: ?>
          <div style="display: grid; gap: 15px;">
            <?php foreach ($customers['customers'] as $customer): ?>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
              <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 20px; align-items: center;">
                
                <!-- Customer Info -->
                <div>
                  <h3 style="margin: 0 0 10px 0; color: #28a745;">
                    <?php echo htmlspecialchars($customer->getGivenName() . ' ' . $customer->getFamilyName()); ?>
                  </h3>
                  <div style="display: flex; gap: 20px; color: #666;">
                    <span>📧 <?php echo htmlspecialchars($customer->getEmailAddress() ?? 'No email'); ?></span>
                    <span>📞 <?php echo htmlspecialchars($customer->getPhoneNumber() ?? 'No phone'); ?></span>
                  </div>
                  <div style="margin-top: 10px;">
                    <span style="background: #17a2b8; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
                      🆔 <?php echo htmlspecialchars($customer->getId()); ?>
                    </span>
                    <span style="background: #6c757d; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em;">
                      📅 <?php echo date('M j, Y', strtotime($customer->getCreatedAt())); ?>
                    </span>
                  </div>
                </div>
                
                <!-- Customer Actions -->
                <div style="display: flex; flex-direction: column; gap: 8px;">
                  <button onclick="viewCustomerDetails('<?php echo $customer->getId(); ?>')" 
                          style="background: #17a2b8; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8em;">
                    👁️ View Details
                  </button>
                  <button onclick="editCustomer('<?php echo $customer->getId(); ?>')" 
                          style="background: #ffc107; color: #212529; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8em;">
                    ✏️ Edit
                  </button>
                  <button onclick="customerHistory('<?php echo $customer->getId(); ?>')" 
                          style="background: #28a745; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8em;">
                    📊 History
                  </button>
                </div>
                
                <!-- Quick Actions -->
                <div style="display: flex; flex-direction: column; gap: 8px;">
                  <button onclick="createInvoice('<?php echo $customer->getId(); ?>')" 
                          style="background: #0077cc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    📄 Invoice
                  </button>
                  <button onclick="bookAppointment('<?php echo $customer->getId(); ?>')" 
                          style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    📅 Book
                  </button>
                </div>
              </div>
            </div>
            <?php endforeach; ?>
          </div>
          <?php endif; ?>
        <?php else: ?>
        <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
          <h3>❌ Error Loading Customers</h3>
          <p><?php echo htmlspecialchars($customers['error']); ?></p>
          <p><strong>Note:</strong> This is expected when Square SDK is not installed or configured.</p>
        </div>
        
        <!-- Mock Customer Data for Demo -->
        <div style="margin-top: 20px;">
          <h3>📋 Demo Customer Data</h3>
          <div style="display: grid; gap: 15px;">
            <?php 
            $mockCustomers = [
              ['name' => 'John Crypto', 'email' => '<EMAIL>', 'phone' => '******-555-0101', 'services' => 'Consulting, Mining Pool'],
              ['name' => 'Sarah Blockchain', 'email' => '<EMAIL>', 'phone' => '******-555-0102', 'services' => 'Market Analysis'],
              ['name' => 'Mike Trader', 'email' => '<EMAIL>', 'phone' => '******-555-0103', 'services' => 'Mining Services']
            ];
            
            foreach ($mockCustomers as $customer): ?>
            <div style="background: #e2e3e5; padding: 15px; border-radius: 8px; border-left: 4px solid #6c757d;">
              <div style="display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: center;">
                <div>
                  <h4 style="margin: 0 0 8px 0;"><?php echo $customer['name']; ?> <span style="color: #6c757d; font-size: 0.8em;">(Demo)</span></h4>
                  <div style="display: flex; gap: 15px; color: #666; font-size: 0.9em;">
                    <span>📧 <?php echo $customer['email']; ?></span>
                    <span>📞 <?php echo $customer['phone']; ?></span>
                  </div>
                  <div style="margin-top: 8px;">
                    <span style="background: #6c757d; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">
                      🛍️ <?php echo $customer['services']; ?>
                    </span>
                  </div>
                </div>
                <div>
                  <span style="color: #6c757d; font-size: 0.8em;">Demo Data</span>
                </div>
              </div>
            </div>
            <?php endforeach; ?>
          </div>
        </div>
        <?php endif; ?>
      </div>

      <!-- Customer Analytics -->
      <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 30px; border: 1px solid #ddd;">
        <h2>📊 Customer Analytics</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px;">
          
          <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>👥 Total Customers</h3>
            <p style="font-size: 2.5em; margin: 10px 0; color: #0077cc;">
              <?php echo $customers['success'] ? count($customers['customers']) : '3'; ?>
            </p>
            <small style="color: #666;">Active customers</small>
          </div>
          
          <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>💰 Avg. Order Value</h3>
            <p style="font-size: 2.5em; margin: 10px 0; color: #28a745;">$287</p>
            <small style="color: #666;">Per customer</small>
          </div>
          
          <div style="background: #f8f0ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>🔄 Repeat Rate</h3>
            <p style="font-size: 2.5em; margin: 10px 0; color: #6c757d;">67%</p>
            <small style="color: #666;">Return customers</small>
          </div>
          
          <div style="background: #fff8f0; padding: 15px; border-radius: 8px; text-align: center;">
            <h3>⭐ Satisfaction</h3>
            <p style="font-size: 2.5em; margin: 10px 0; color: #ffc107;">4.8</p>
            <small style="color: #666;">Average rating</small>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="square-admin-dashboard.php" class="cta-button" style="text-decoration: none; margin-right: 10px;">
          ← Admin Dashboard
        </a>
        <a href="square-product-manager.php" class="cta-button" style="text-decoration: none; margin-right: 10px; background: #28a745;">
          📦 Product Manager
        </a>
        <a href="multi-gateway-payment-form.php" class="cta-button" style="text-decoration: none; background: #6c757d;">
          💳 Payment Form
        </a>
      </div>
    </div>
  </section>
</main>

<script>
console.log('Square Customer Manager loaded');

// Form management
function showAddCustomerForm() {
  document.getElementById('addCustomerForm').style.display = 'block';
  document.getElementById('firstName').focus();
}

function hideAddCustomerForm() {
  document.getElementById('addCustomerForm').style.display = 'none';
  document.getElementById('customerForm').reset();
}

// Customer form submission
document.getElementById('customerForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const customerData = {
    given_name: formData.get('firstName'),
    family_name: formData.get('lastName'),
    email: formData.get('email'),
    phone: formData.get('phone')
  };
  
  console.log('Creating customer:', customerData);
  alert('Customer would be created in Square (requires Square SDK and credentials)');
  
  hideAddCustomerForm();
});

// Customer search
document.getElementById('customerSearch').addEventListener('input', function(e) {
  const query = e.target.value.toLowerCase();
  console.log('Searching customers:', query);
  // In a real implementation, this would filter the customer list or make an API call
});

// Customer management functions
function viewCustomerDetails(customerId) {
  alert(`View customer details: ${customerId} (requires Square SDK)`);
}

function editCustomer(customerId) {
  alert(`Edit customer: ${customerId} (requires Square SDK)`);
}

function customerHistory(customerId) {
  alert(`View customer history: ${customerId} (requires Square SDK and transaction data)`);
}

function createInvoice(customerId) {
  alert(`Create invoice for customer: ${customerId} (requires Square Invoices API)`);
}

function bookAppointment(customerId) {
  alert(`Book appointment for customer: ${customerId} (requires Square Bookings API)`);
}

// Add visual feedback for form interactions
document.querySelectorAll('input').forEach(element => {
  element.addEventListener('focus', function() {
    this.style.borderColor = '#0077cc';
    this.style.boxShadow = '0 0 0 3px rgba(0, 119, 204, 0.1)';
  });
  
  element.addEventListener('blur', function() {
    this.style.borderColor = '#ccc';
    this.style.boxShadow = 'none';
  });
});
</script>

<?php include "footer.php"; ?>
